export interface User {
  id: string;
  username: string;
  email?: string;
  createdAt: Date;
}

export interface Subreddit {
  name: string;
  description: string;
  createdAt: Date;
  members: number;
}

export interface Post {
  id: string;
  title: string;
  content: string;
  authorId: string;
  subreddit: string;
  upvotes: number;
  downvotes: number;
  createdAt: Date;
  comments: Comment[];
}

export interface Comment {
  id: string;
  content: string;
  authorId: string;
  postId: string;
  parentId?: string;
  upvotes: number;
  downvotes: number;
  createdAt: Date;
  replies: Comment[];
}