import { MongoClient } from 'mongodb';

const uri = process.env.MONGODB_URI || 'mongodb://localhost:27017/reddit-clone';
const client = new MongoClient(uri);

export async function connectDB() {
	try {
		await client.connect();
		console.log('Connected to MongoDB');
		return client.db('reddit-clone');
	} catch (error) {
		console.error('MongoDB connection error:', error);
		throw error;
	}
}

export { client };