import { writable } from 'svelte/store';
import type { Post, Comment, User, Subreddit } from './types';

// Mock data
const mockUsers: User[] = [
  { id: '1', username: 'user1', createdAt: new Date() },
  { id: '2', username: 'user2', createdAt: new Date() }
];

const mockPosts: Post[] = [
  {
    id: '1',
    title: 'Welcome to Reddit Clone',
    content: 'This is the first post! Excited to see what the community shares.',
    authorId: '1',
    subreddit: 'general',
    upvotes: 42,
    downvotes: 3,
    createdAt: new Date(Date.now() - 3600000),
    comments: []
  },
  {
    id: '2',
    title: 'New JavaScript framework released',
    content: 'Just heard about this new framework. Has anyone tried it? What are your thoughts?',
    authorId: '2',
    subreddit: 'tech',
    upvotes: 28,
    downvotes: 1,
    createdAt: new Date(Date.now() - 7200000),
    comments: []
  },
  {
    id: '3',
    title: 'Best practices for Svelte development',
    content: 'What are some best practices you follow when developing with Svelte?',
    authorId: '1',
    subreddit: 'programming',
    upvotes: 15,
    downvotes: 0,
    createdAt: new Date(Date.now() - 10800000),
    comments: []
  },
  {
    id: '4',
    title: 'Weekend project ideas',
    content: 'Looking for some fun weekend project ideas. Any suggestions?',
    authorId: '2',
    subreddit: 'general',
    upvotes: 8,
    downvotes: 2,
    createdAt: new Date(Date.now() - 14400000),
    comments: []
  }
];

export const posts = writable<Post[]>(mockPosts);
export const users = writable<User[]>(mockUsers);

export function upvotePost(postId: string) {
	posts.update(p => p.map(post => post.id === postId ? { ...post, upvotes: post.upvotes + 1 } : post));
}

export function downvotePost(postId: string) {
	posts.update(p => p.map(post => post.id === postId ? { ...post, downvotes: post.downvotes + 1 } : post));
}