<script lang="ts">
	import { posts, upvotePost, downvotePost } from '$lib/stores';
	import { page } from '$app/stores';
	import type { Post } from '$lib/types';

	$: postId = $page.params.id;
	$: post = $posts.find(p => p.id === postId);

	function handleUpvote() {
		if (postId) upvotePost(postId);
	}

	function handleDownvote() {
		if (postId) downvotePost(postId);
	}
</script>

<svelte:head>
	<title>{post?.title || 'Post'} - Reddit Clone</title>
</svelte:head>

{#if post}
	<div class="bg-gray-100 min-h-screen">
		<div class="max-w-4xl mx-auto p-4">
			<div class="bg-white rounded shadow mb-4">
				<div class="flex">
					<div class="flex flex-col items-center p-2 bg-gray-50 rounded-l">
						<button class="text-gray-400 hover:text-orange-500 text-lg" on:click={handleUpvote}>▲</button>
						<span class="text-sm font-medium">{post.upvotes - post.downvotes}</span>
						<button class="text-gray-400 hover:text-blue-500 text-lg" on:click={handleDownvote}>▼</button>
					</div>
					<div class="flex-1 p-4">
						<h1 class="text-xl font-medium mb-1">{post.title}</h1>
						<p class="text-sm text-gray-500 mb-2">
							Posted by u/{post.authorId} in r/{post.subreddit} • {new Date(post.createdAt).toLocaleDateString()}
						</p>
						<p class="mb-4">{post.content}</p>
					</div>
				</div>
			</div>
			<div class="bg-white rounded shadow p-4">
				<h2 class="text-lg font-medium mb-4">Comments</h2>
				{#if post.comments.length === 0}
					<p class="text-gray-500">No comments yet.</p>
				{:else}
					<!-- Comments would go here -->
				{/if}
			</div>
		</div>
	</div>
{:else}
	<div class="bg-gray-100 min-h-screen flex items-center justify-center">
		<div class="text-center">
			<h1 class="text-2xl font-bold mb-2">Post not found</h1>
			<p class="text-gray-600">The post you're looking for doesn't exist.</p>
		</div>
	</div>
{/if}