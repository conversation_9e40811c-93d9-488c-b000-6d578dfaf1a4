{"name": "reddit-clone", "private": true, "version": "0.0.1", "type": "module", "scripts": {"dev": "vite dev", "build": "vite build", "preview": "vite preview", "prepare": "svelte-kit sync || echo ''", "check": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json", "check:watch": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json --watch"}, "devDependencies": {"@fontsource/fira-mono": "^5.2.7", "@neoconfetti/svelte": "^2.2.2", "@sveltejs/adapter-auto": "^6.1.0", "@sveltejs/kit": "^2.43.2", "@sveltejs/vite-plugin-svelte": "^6.2.0", "@tailwindcss/postcss": "^4.1.14", "autoprefixer": "^10.4.21", "postcss": "^8.5.6", "svelte": "^5.39.5", "svelte-check": "^4.3.2", "tailwindcss": "^4.1.14", "typescript": "^5.9.2", "vite": "^7.1.7"}, "dependencies": {"mongodb": "^6.20.0"}}