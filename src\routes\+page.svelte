<script lang="ts">
	import { posts } from '$lib/stores';
	import type { Post } from '$lib/types';

	function handleMouseOver(e) {
		e.target.style.color = '#2563eb';
	}

	function handleMouseOut(e) {
		e.target.style.color = '#111827';
	}

	function handleCardMouseOver(e) {
		e.target.style.boxShadow = '0 4px 6px rgba(0,0,0,0.1)';
	}

	function handleCardMouseOut(e) {
		e.target.style.boxShadow = 'none';
	}
</script>

<svelte:head>
	<title>Home - Reddit Clone</title>
</svelte:head>

<div style="min-height: 100vh; background-color: white;">
	<div style="max-width: 48rem; margin: 0 auto; padding: 1.5rem;">
		<div style="margin-bottom: 1.5rem;">
			<h1 style="font-size: 1.875rem; font-weight: bold; color: #111827; margin-bottom: 0.5rem;">Reddit Clone</h1>
			<p style="color: #6b7280;">Discover and share content</p>
		</div>
		{#each $posts as post (post.id)}
			<div style="background-color: white; border: 1px solid #e5e7eb; border-radius: 0.5rem; padding: 1.5rem; margin-bottom: 1.5rem; transition: box-shadow 0.2s;" on:mouseover={handleCardMouseOver} on:mouseout={handleCardMouseOut}>
				<h2 style="font-size: 1.25rem; font-weight: 600; color: #111827; margin-bottom: 0.5rem;">
					<a href="/post/{post.id}" style="color: #111827;" on:mouseover={handleMouseOver} on:mouseout={handleMouseOut}>{post.title}</a>
				</h2>
				<p style="color: #374151; margin-bottom: 0.75rem;">{post.content}</p>
				<div style="display: flex; justify-content: space-between; font-size: 0.875rem; color: #6b7280;">
					<span>Posted by u/{post.authorId} in r/{post.subreddit}</span>
					<span>{new Date(post.createdAt).toLocaleDateString()}</span>
				</div>
				<div style="display: flex; align-items: center; gap: 1rem; margin-top: 0.75rem; font-size: 0.875rem;">
					<a href="/post/{post.id}" style="color: #2563eb;">{post.comments.length} comments</a>
					<button style="color: #4b5563;">Share</button>
					<button style="color: #4b5563;">Save</button>
				</div>
			</div>
		{/each}
	</div>
</div>
