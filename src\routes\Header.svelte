<script lang="ts">
	import { page } from '$app/state';
</script>

<header style="background-color: white; border-bottom: 1px solid #e5e7eb; padding: 1rem; display: flex; align-items: center; justify-content: space-between; box-shadow: 0 1px 3px rgba(0,0,0,0.1);">
	<div style="display: flex; align-items: center;">
		<a href="/" style="font-size: 1.5rem; font-weight: bold; color: #111827;">Reddit Clone</a>
	</div>
	<div style="flex: 1; margin: 0 1rem; max-width: 32rem;">
		<div style="position: relative;">
			<input type="text" placeholder="Search Reddit" style="width: 100%; padding: 0.75rem 1rem 0.75rem 2.5rem; border-radius: 9999px; border: 1px solid #d1d5db; outline: none;" />
			<svg style="position: absolute; left: 0.75rem; top: 0.75rem; height: 1.25rem; width: 1.25rem; color: #9ca3af;" fill="none" stroke="currentColor" viewBox="0 0 24 24">
				<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
			</svg>
		</div>
	</div>
	<div>
		<button style="color: #4b5563; padding: 0.5rem 1rem; border-radius: 0.375rem; margin-right: 0.5rem;">Log In</button>
		<button style="background-color: #2563eb; color: white; padding: 0.5rem 1rem; border-radius: 0.375rem;">Sign Up</button>
	</div>
</header>
